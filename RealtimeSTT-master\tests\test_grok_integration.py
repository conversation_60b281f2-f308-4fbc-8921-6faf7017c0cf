"""
Test script for Grok API integration with RealtimeSTT.

This script tests the Grok API client functionality including:
- Authentication
- Text generation
- Streaming responses
- Error handling
- Integration with RealtimeSTT

Author: RealtimeSTT Integration
"""

import os
import sys
import time
import unittest
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to import RealtimeSTT
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from RealtimeSTT import GrokClient, GrokAPIError


class TestGrokIntegration(unittest.TestCase):
    """Test cases for Grok API integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Use a test API key or mock
        self.test_api_key = os.environ.get("XAI_API_KEY", "test_key")
        self.client = None
        
    def test_client_initialization(self):
        """Test Grok client initialization."""
        # Test with API key
        client = GrokClient(api_key="test_key")
        self.assertIsNotNone(client)
        self.assertEqual(client.api_key, "test_key")
        
        # Test with environment variable
        with patch.dict(os.environ, {'XAI_API_KEY': 'env_test_key'}):
            client = GrokClient()
            self.assertEqual(client.api_key, "env_test_key")
        
        # Test without API key should raise error
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError):
                GrokClient()
    
    def test_api_error_handling(self):
        """Test API error handling."""
        client = GrokClient(api_key="invalid_key")
        
        # Mock a 401 response
        with patch.object(client.session, 'request') as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 401
            mock_request.return_value = mock_response
            
            with self.assertRaises(GrokAPIError) as context:
                client._make_request("GET", "/test")
            
            self.assertEqual(context.exception.status_code, 401)
            self.assertEqual(context.exception.error_type, "authentication")
    
    def test_rate_limit_handling(self):
        """Test rate limit error handling."""
        client = GrokClient(api_key="test_key")
        
        # Mock a 429 response
        with patch.object(client.session, 'request') as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 429
            mock_request.return_value = mock_response
            
            with self.assertRaises(GrokAPIError) as context:
                client._make_request("GET", "/test")
            
            self.assertEqual(context.exception.status_code, 429)
            self.assertEqual(context.exception.error_type, "rate_limit")
    
    @patch('requests.Session.request')
    def test_list_models(self, mock_request):
        """Test listing available models."""
        client = GrokClient(api_key="test_key")
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [
                {"id": "grok-4", "object": "model"},
                {"id": "grok-2", "object": "model"}
            ]
        }
        mock_request.return_value = mock_response
        
        models = client.list_models()
        self.assertEqual(len(models), 2)
        self.assertEqual(models[0]["id"], "grok-4")
    
    @patch('requests.Session.request')
    def test_chat_completion(self, mock_request):
        """Test chat completion functionality."""
        client = GrokClient(api_key="test_key")
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "Hello! How can I help you today?"
                    }
                }
            ]
        }
        mock_request.return_value = mock_response
        
        messages = [{"role": "user", "content": "Hello"}]
        response = client.chat_completion(messages=messages)
        
        self.assertIn("choices", response)
        self.assertEqual(
            response["choices"][0]["message"]["content"],
            "Hello! How can I help you today?"
        )
    
    @patch('requests.Session.request')
    def test_generate_text_convenience(self, mock_request):
        """Test the generate_text convenience method."""
        client = GrokClient(api_key="test_key")
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "This is a test response."
                    }
                }
            ]
        }
        mock_request.return_value = mock_response
        
        response = client.generate_text("Test prompt")
        self.assertEqual(response, "This is a test response.")
    
    @patch('requests.Session.request')
    def test_streaming_response(self, mock_request):
        """Test streaming chat completion."""
        client = GrokClient(api_key="test_key")
        
        # Mock streaming response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',
            b'data: {"choices": [{"delta": {"content": " world"}}]}',
            b'data: [DONE]'
        ]
        mock_request.return_value = mock_response
        
        messages = [{"role": "user", "content": "Hello"}]
        chunks = list(client.chat_completion(messages=messages, stream=True))
        
        self.assertEqual(len(chunks), 2)
        self.assertEqual(chunks[0]["choices"][0]["delta"]["content"], "Hello")
        self.assertEqual(chunks[1]["choices"][0]["delta"]["content"], " world")
    
    def test_timeout_handling(self):
        """Test timeout error handling."""
        client = GrokClient(api_key="test_key", timeout=0.001)  # Very short timeout
        
        with patch.object(client.session, 'request') as mock_request:
            mock_request.side_effect = Exception("Timeout")
            
            with self.assertRaises(GrokAPIError) as context:
                client._make_request("GET", "/test")
            
            self.assertIn("Request failed", str(context.exception))


class TestGrokIntegrationLive(unittest.TestCase):
    """Live integration tests (requires valid API key)."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.api_key = os.environ.get("XAI_API_KEY")
        if not self.api_key:
            self.skipTest("XAI_API_KEY environment variable not set")
        
        self.client = GrokClient(api_key=self.api_key)
    
    def test_live_list_models(self):
        """Test listing models with live API."""
        try:
            models = self.client.list_models()
            self.assertIsInstance(models, list)
            self.assertGreater(len(models), 0)
            print(f"Available models: {[model.get('id', 'unknown') for model in models]}")
        except GrokAPIError as e:
            self.skipTest(f"API error: {e}")
    
    def test_live_simple_completion(self):
        """Test simple completion with live API."""
        try:
            response = self.client.generate_text(
                "Say 'Hello, RealtimeSTT!' in exactly those words.",
                max_tokens=50
            )
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 0)
            print(f"Response: {response}")
        except GrokAPIError as e:
            self.skipTest(f"API error: {e}")
    
    def test_live_streaming_completion(self):
        """Test streaming completion with live API."""
        try:
            chunks = []
            for chunk in self.client.generate_stream(
                "Count from 1 to 3, one number per response chunk.",
                max_tokens=20
            ):
                chunks.append(chunk)
                if len(chunks) >= 5:  # Limit chunks to avoid long test
                    break
            
            self.assertGreater(len(chunks), 0)
            full_response = "".join(chunks)
            print(f"Streaming response: {full_response}")
        except GrokAPIError as e:
            self.skipTest(f"API error: {e}")


def run_basic_tests():
    """Run basic functionality tests."""
    print("Running basic Grok API tests...")
    
    # Test client creation
    try:
        client = GrokClient(api_key="test_key")
        print("✓ Client creation successful")
    except Exception as e:
        print(f"✗ Client creation failed: {e}")
        return False
    
    # Test error handling
    try:
        client = GrokClient(api_key="invalid_key")
        # This should work for creation, but fail on actual API calls
        print("✓ Error handling setup successful")
    except Exception as e:
        print(f"✗ Error handling setup failed: {e}")
        return False
    
    return True


def run_live_tests():
    """Run live API tests if API key is available."""
    api_key = os.environ.get("XAI_API_KEY")
    if not api_key:
        print("Skipping live tests - XAI_API_KEY not set")
        return True
    
    print("Running live Grok API tests...")
    
    try:
        client = GrokClient(api_key=api_key)
        
        # Test simple generation
        response = client.generate_text(
            "Respond with exactly: 'Grok integration test successful'",
            max_tokens=20
        )
        print(f"✓ Live generation test: {response}")
        
        # Test streaming
        chunks = []
        for chunk in client.generate_stream(
            "Say 'Streaming works' word by word.",
            max_tokens=10
        ):
            chunks.append(chunk)
            if len(chunks) >= 3:
                break
        
        streaming_response = "".join(chunks)
        print(f"✓ Live streaming test: {streaming_response}")
        
        return True
        
    except Exception as e:
        print(f"✗ Live tests failed: {e}")
        return False


if __name__ == "__main__":
    print("=" * 50)
    print("Grok API Integration Test Suite")
    print("=" * 50)
    
    # Run basic tests
    basic_success = run_basic_tests()
    
    # Run live tests if API key is available
    live_success = run_live_tests()
    
    # Run unittest suite
    print("\nRunning unittest suite...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Basic tests: {'PASSED' if basic_success else 'FAILED'}")
    print(f"Live tests: {'PASSED' if live_success else 'FAILED'}")
    print("=" * 50)
