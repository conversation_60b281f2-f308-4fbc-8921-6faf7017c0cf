"""
Simple Auto-typing example for RealtimeSTT

This is a minimal example showing how to enable auto-typing functionality.
Your speech will be automatically typed into any active text input field.

Usage:
1. Run this script
2. Open any text application (notepad, word processor, browser text field, etc.)
3. Click in a text input area to give it focus
4. Speak into your microphone
5. Watch your speech appear as typed text!

Press Ctrl+C to exit.
"""

import os
import sys

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


def main():
    print("🎯 Simple Auto-Typing Example")
    print("=" * 35)
    print("Speak and watch your words appear in any text field!")
    print("Press Ctrl+C to exit.")
    print()
    
    try:
        # Create recorder with auto-typing enabled
        recorder = AudioToTextRecorder(
            model="tiny",  # Fast model for quick response
            enable_auto_typing=True,  # Enable auto-typing
            spinner=True,
        )
        
        print("✅ Ready! Open a text field and start speaking...")
        
        # Main loop
        while True:
            try:
                # This will automatically type the transcribed text
                text = recorder.text()
                if text.strip():
                    print(f"Typed: '{text}'")
                    
            except KeyboardInterrupt:
                print("\nExiting...")
                break
                
    except ImportError as e:
        if "pyautogui" in str(e):
            print("❌ Error: pyautogui is required for auto-typing.")
            print("Install it with: pip install pyautogui")
        else:
            print(f"❌ Import error: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
        
    finally:
        try:
            recorder.shutdown()
        except:
            pass


if __name__ == "__main__":
    main()
