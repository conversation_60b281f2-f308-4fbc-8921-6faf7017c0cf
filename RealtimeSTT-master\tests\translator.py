import os
import sys
from RealtimeSTT import AudioToTextRecorder, GrokClient
from RealtimeTTS import TextToAudioStream, SystemEngine

# Add parent directory to path for load_env import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from load_env import get_api_key

if __name__ == '__main__':
    # Load API key from .env file
    api_key = get_api_key()
    if not api_key:
        print("Please set your XAI_API_KEY in the .env file")
        sys.exit(1)

    # Setup Grok API client
    grok_client = GrokClient(api_key=api_key)

    # Text-to-Speech Stream Setup (using SystemEngine)
    engine = SystemEngine()
    stream = TextToAudioStream(engine, log_characters=True)

    # Speech-to-Text Recorder Setup
    recorder = AudioToTextRecorder(
        model="medium",
    )

    # Supported languages (using system voices)
    languages = [
        ["english", "English"],
        ["german", "German"],
        ["french", "French"],
        ["spanish", "Spanish"],
        ["portuguese", "Portuguese"],
        ["italian", "Italian"]
    ]

    def generate_response(messages):
        """Generate assistant's response using Grok."""
        for chunk in grok_client.chat_completion(model="grok-4", messages=messages, stream=True):
            if "choices" in chunk and len(chunk["choices"]) > 0:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta and delta["content"]:
                    yield delta["content"]
                
    def clear_console():
        os.system('clear' if os.name == 'posix' else 'cls')

    def select_language():
        """Display language options and get user's choice."""
        for index, language in enumerate(languages, start=1):
            print(f"{index}. {language[0]}")
        language_number = input("Select language to translate to (1-6): ")
        return languages[int(language_number) - 1]

    def main():
        """Main translation loop."""
        clear_console()
        language_info = select_language()
        engine.set_voice(language_info[1])

        system_prompt_message = {
            'role': 'system',
            'content': f'Translate the given text to {language_info[0]}. Output only the translated text.'
        }

        while True:
            print("\nSay something!")

            # Capture user input from microphone
            user_text = recorder.text()
            print(f"Input text: {user_text}")

            user_message = {'role': 'user', 'content': user_text}

            # Get assistant response and play it
            translation_stream = generate_response([system_prompt_message, user_message])
            print("Translation: ", end="", flush=True)
            stream.feed(translation_stream)
            stream.play()

    main()