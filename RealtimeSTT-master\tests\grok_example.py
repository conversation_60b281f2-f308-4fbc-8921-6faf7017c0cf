"""
Simple example demonstrating Grok API integration with RealtimeSTT.

This script shows how to:
1. Set up the Grok client
2. Use speech-to-text with RealtimeSTT
3. Process the text with Grok API
4. Handle responses and errors

Requirements:
- Set XAI_API_KEY environment variable
- Install dependencies: pip install requests

Author: RealtimeSTT Integration
"""

import os
import sys

# Add the parent directory to the path to import RealtimeSTT
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from RealtimeSTT import AudioToTextRecorder, GrokClient, GrokAPIError
from load_env import get_api_key


def main():
    """Main example function."""
    print("=" * 60)
    print("RealtimeSTT + Grok API Integration Example")
    print("=" * 60)

    # Load API key from .env file
    api_key = get_api_key()
    if not api_key:
        print("❌ Error: XAI_API_KEY not found!")
        print("Please set your xAI API key in the .env file:")
        print("   1. Edit the .env file in the project root")
        print("   2. Replace 'your-xai-api-key-here' with your actual API key")
        print("   3. Get your API key from: https://console.x.ai/")
        return
    
    try:
        # Initialize Grok client
        print("🤖 Initializing Grok client...")
        grok_client = GrokClient(api_key=api_key)
        print("✅ Grok client initialized successfully!")
        
        # Test Grok API connection
        print("\n🔍 Testing Grok API connection...")
        test_response = grok_client.generate_text(
            "Respond with exactly: 'Connection successful!'",
            max_tokens=10
        )
        print(f"✅ API test response: {test_response}")
        
        # Initialize speech-to-text recorder
        print("\n🎤 Initializing speech-to-text recorder...")
        recorder = AudioToTextRecorder(
            model="base",  # Use smaller model for faster startup
            language="en",
            spinner=True
        )
        print("✅ Speech-to-text recorder initialized!")
        
        # Interactive loop
        print("\n" + "=" * 60)
        print("🎯 Ready! Speak into your microphone.")
        print("   - Your speech will be converted to text")
        print("   - Text will be processed by Grok")
        print("   - Press Ctrl+C to exit")
        print("=" * 60)
        
        conversation_history = []
        
        while True:
            try:
                print("\n🎤 Listening... (speak now)")
                
                # Record and transcribe speech
                text = recorder.text()
                
                if text.strip():
                    print(f"📝 You said: '{text}'")
                    
                    # Add user message to conversation
                    conversation_history.append({
                        "role": "user", 
                        "content": text
                    })
                    
                    # Keep conversation history manageable
                    if len(conversation_history) > 10:
                        conversation_history = conversation_history[-8:]
                    
                    print("🤖 Grok is thinking...")
                    
                    # Get response from Grok
                    try:
                        # Use streaming for real-time response
                        print("💬 Grok: ", end="", flush=True)
                        
                        response_chunks = []
                        for chunk in grok_client.chat_completion(
                            messages=conversation_history,
                            model="grok-4",
                            stream=True,
                            max_tokens=150,
                            temperature=0.7
                        ):
                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                delta = chunk["choices"][0].get("delta", {})
                                if "content" in delta and delta["content"]:
                                    content = delta["content"]
                                    print(content, end="", flush=True)
                                    response_chunks.append(content)
                        
                        print()  # New line after response
                        
                        # Add assistant response to conversation
                        full_response = "".join(response_chunks)
                        conversation_history.append({
                            "role": "assistant",
                            "content": full_response
                        })
                        
                    except GrokAPIError as e:
                        print(f"❌ Grok API Error: {e}")
                        if e.status_code == 401:
                            print("   Check your API key!")
                        elif e.status_code == 429:
                            print("   Rate limit exceeded. Please wait a moment.")
                        else:
                            print(f"   Status code: {e.status_code}")
                
                else:
                    print("🔇 No speech detected. Try speaking louder or closer to the microphone.")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                print("Continuing...")
                
    except GrokAPIError as e:
        print(f"❌ Grok API Error during initialization: {e}")
        if e.status_code == 401:
            print("   Your API key might be invalid. Please check it.")
        else:
            print(f"   Status code: {e.status_code}")
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        print("Please check your setup and try again.")


def test_grok_only():
    """Test only the Grok API without speech recognition."""
    print("🧪 Testing Grok API only...")
    
    api_key = os.environ.get("XAI_API_KEY")
    if not api_key:
        print("❌ XAI_API_KEY not set!")
        return
    
    try:
        client = GrokClient(api_key=api_key)
        
        # Test simple generation
        print("📝 Testing simple text generation...")
        response = client.generate_text(
            "Explain what xAI's Grok is in one sentence.",
            max_tokens=50
        )
        print(f"✅ Response: {response}")
        
        # Test streaming
        print("\n📡 Testing streaming response...")
        print("🤖 Grok: ", end="", flush=True)
        for chunk in client.generate_stream(
            "Count from 1 to 5, saying each number clearly.",
            max_tokens=30
        ):
            print(chunk, end="", flush=True)
        print()
        
        # Test conversation
        print("\n💬 Testing conversation...")
        messages = [
            {"role": "user", "content": "Hello! What's your name?"},
        ]
        
        response = client.chat_completion(messages=messages, max_tokens=50)
        assistant_response = response["choices"][0]["message"]["content"]
        print(f"✅ Conversation response: {assistant_response}")
        
        print("\n🎉 All Grok API tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test-grok-only":
        test_grok_only()
    else:
        main()
