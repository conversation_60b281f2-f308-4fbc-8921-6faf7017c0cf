# Grok API Setup Guide

This guide will help you set up your Grok API key for use with RealtimeSTT examples.

## Step 1: Get Your Grok API Key

1. Visit [xAI Console](https://console.x.ai/)
2. Create an account or sign in
3. Navigate to the API Keys section
4. Generate a new API key
5. Copy the API key (it will look like `xai-...`)

## Step 2: Configure Your API Key

### Method 1: Using .env File (Recommended)

1. **Edit the .env file** in the project root directory:
   ```
   XAI_API_KEY=your-actual-api-key-here
   ```

2. **Replace the placeholder** with your actual API key:
   ```
   XAI_API_KEY=xai-1234567890abcdef...
   ```

3. **Save the file**

### Method 2: Environment Variable (Alternative)

**Windows:**
```cmd
set XAI_API_KEY=your-actual-api-key-here
```

**Linux/macOS:**
```bash
export XAI_API_KEY=your-actual-api-key-here
```

## Step 3: Test Your Setup

Run the test script to verify your API key works:

```bash
python load_env.py
```

You should see:
```
✅ API key loaded successfully: xai-123456...
```

## Step 4: Run Examples

Now you can run any of the Grok-enabled examples:

### Basic Talkbot
```bash
python tests/minimalistic_talkbot.py
```

### Voice Interface
```bash
python tests/openai_voice_interface.py
```

### Translator
```bash
python tests/translator.py
```

### GUI Application
```bash
python example_app/ui_openai_voice_interface.py
```

## Security Notes

- ✅ The `.env` file is automatically ignored by git (listed in `.gitignore`)
- ✅ Never commit your actual API key to version control
- ✅ Keep your API key secure and don't share it publicly
- ✅ The `.env` file method keeps your key separate from your code

## Troubleshooting

### "API key not found" Error
- Check that you've edited the `.env` file correctly
- Make sure you replaced `your-xai-api-key-here` with your actual key
- Verify the file is saved in the project root directory

### "Invalid API key" Error
- Double-check your API key is correct
- Make sure you copied the full key from the xAI console
- Try generating a new API key if the current one doesn't work

### Import Errors
- Make sure you have the required dependencies: `pip install requests`
- Ensure you're running from the correct directory

## Files Updated for .env Support

The following files have been updated to automatically load your API key from the `.env` file:

- `tests/minimalistic_talkbot.py`
- `tests/grok_example.py`
- `tests/translator.py`
- `tests/openai_voice_interface.py`
- `example_app/ui_openai_voice_interface.py`
- `example_app/start.bat`

All these files now use the `load_env.py` utility to consistently load your API key.
