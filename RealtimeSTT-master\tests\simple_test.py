if __name__ == '__main__':

    import os
    import sys
    if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()

    from RealtimeSTT import AudioToTextRecorder

    # Example 1: Traditional console output
    print("=== Example 1: Console Output ===")
    recorder = AudioToTextRecorder(
        spinner=False,
        silero_sensitivity=0.01,
        model="tiny.en",
        language="en",
        )

    print("Say something (will be displayed in console)...")

    try:
        for i in range(3):  # Just a few examples
            text = recorder.text()
            print("Detected text: " + text)
    except KeyboardInterrupt:
        print("Interrupted")

    recorder.shutdown()

    # Example 2: Auto-typing mode
    print("\n=== Example 2: Auto-typing Mode ===")
    print("Open a text field and speak - text will be automatically typed!")
    print("Press Ctrl+C to exit.")

    try:
        recorder_auto = AudioToTextRecorder(
            spinner=False,
            silero_sensitivity=0.01,
            model="tiny.en",
            language="en",
            enable_auto_typing=True,  # Enable auto-typing
            auto_typing_delay=0.01,
            )

        print("Ready for auto-typing! Speak now...")

        while True:
            text = recorder_auto.text()
            if text.strip():
                print(f"Auto-typed: '{text}'")

    except KeyboardInterrupt:
        print("Exiting application due to keyboard interrupt")
    except ImportError as e:
        if "pyautogui" in str(e):
            print("Note: pyautogui not available for auto-typing feature")
        else:
            print(f"Import error: {e}")
    finally:
        try:
            recorder_auto.shutdown()
        except:
            pass
