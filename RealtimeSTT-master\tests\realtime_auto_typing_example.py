"""
Real-time Auto-typing example for RealtimeSTT

This example demonstrates real-time auto-typing functionality where
speech is transcribed and typed as you speak, providing immediate
feedback in any active text input field.

Features:
- Real-time transcription with immediate auto-typing
- Stabilized text auto-typing for better accuracy
- Configurable typing speed and behavior
- Visual feedback in the console

Usage:
1. Run this script
2. Open any text editor or text input field
3. Click in the text field to give it focus
4. Start speaking - text will appear as you speak!

Press Ctrl+C to exit.
"""

import os
import sys
import time

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class RealTimeAutoTyper:
    def __init__(self):
        self.last_typed_text = ""
        self.typing_active = False
    
    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True
        print("🖊️  Typing...", end="", flush=True)
    
    def on_typing_complete(self):
        """Callback when auto-typing completes"""
        self.typing_active = False
        print(" ✅")
    
    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error"""
        self.typing_active = False
        print(f" ❌ Error: {error}")
    
    def on_recording_start(self):
        """Callback when recording starts"""
        print("🎤 Listening...")
    
    def on_recording_stop(self):
        """Callback when recording stops"""
        print("⏹️  Processing...")
    
    def on_realtime_update(self, text):
        """Callback for real-time transcription updates"""
        # Show real-time updates but don't type them (too frequent)
        print(f"\r🔄 Live: {text[:50]}{'...' if len(text) > 50 else ''}", end="", flush=True)
    
    def on_realtime_stabilized(self, text):
        """Callback for stabilized real-time transcription"""
        # This text is more stable and will be auto-typed
        if text.strip() and text != self.last_typed_text:
            print(f"\n📝 Stabilized: '{text}'")
            self.last_typed_text = text
            # Auto-typing happens automatically in the AudioToTextRecorder
    
    def run(self):
        print("🎯 RealtimeSTT Real-time Auto-Typing Example")
        print("=" * 55)
        print()
        print("This example provides real-time auto-typing as you speak.")
        print("• Real-time transcription updates are shown in the console")
        print("• Stabilized text is automatically typed into the active text field")
        print("• Open a text editor and click in a text field to see it in action")
        print()
        print("Press Ctrl+C to exit.")
        print()
        
        try:
            # Initialize the recorder with real-time auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="base",  # Main model for final transcription
                language="en",
                
                # Real-time transcription settings
                enable_realtime_transcription=True,
                realtime_model_type="tiny",  # Faster model for real-time
                realtime_processing_pause=0.1,  # Process every 100ms
                on_realtime_transcription_update=self.on_realtime_update,
                on_realtime_transcription_stabilized=self.on_realtime_stabilized,
                
                # Auto-typing configuration (applies to stabilized text)
                enable_auto_typing=True,
                auto_typing_delay=0.005,  # Fast typing for real-time feel
                auto_typing_fail_safe=True,
                auto_typing_add_space=True,
                
                # Auto-typing callbacks
                on_auto_typing_start=self.on_typing_start,
                on_auto_typing_complete=self.on_typing_complete,
                on_auto_typing_error=self.on_typing_error,
                
                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,
                
                # Voice activity detection settings
                silero_sensitivity=0.05,  # Very sensitive for real-time
                webrtc_sensitivity=2,
                post_speech_silence_duration=0.3,  # Quick response
                min_length_of_recording=0.2,
                min_gap_between_recordings=0.1,
                
                # UI settings
                spinner=False,  # Disable spinner for cleaner real-time output
            )
            
            print("🚀 Real-time recorder initialized!")
            print("💡 Tips:")
            print("   • Speak clearly and at normal pace")
            print("   • Stabilized text will be auto-typed")
            print("   • Move mouse to top-left corner for emergency stop")
            print()
            print("👄 Start speaking...")
            
            # Keep the recorder running for real-time transcription
            try:
                while True:
                    time.sleep(0.1)  # Keep the main thread alive
                    
            except KeyboardInterrupt:
                print("\n🛑 Stopping real-time transcription...")
                
        except ImportError as e:
            if "pyautogui" in str(e):
                print("❌ Error: pyautogui is required for auto-typing functionality.")
                print("📦 Install it with: pip install pyautogui")
            else:
                print(f"❌ Import error: {e}")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Failed to initialize recorder: {e}")
            sys.exit(1)
            
        finally:
            try:
                recorder.shutdown()
                print("✅ Recorder shut down successfully")
            except:
                pass


def main():
    typer = RealTimeAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
