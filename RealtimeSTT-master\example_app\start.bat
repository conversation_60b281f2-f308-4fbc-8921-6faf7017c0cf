@echo off
cd /d %~dp0

REM Check if the venv directory exists
if not exist test_env\Scripts\python.exe (
    echo Creating VENV
    python -m venv test_env
) else (
    echo VENV already exists
)

REM Check if .env file exists
if not exist ..\\.env (
    echo.
    echo WARNING: .env file not found!
    echo Please create a .env file in the project root with your XAI_API_KEY
    echo Example: XAI_API_KEY=your-api-key-here
    echo Get your API key from: https://console.x.ai/
    echo.
    pause
)

echo Activating VENV and starting application...
echo Note: API key will be loaded from .env file
start cmd /k "call test_env\Scripts\activate.bat && python ui_openai_voice_interface.py"