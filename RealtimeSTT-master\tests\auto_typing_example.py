"""
Auto-typing example for RealtimeSTT

This example demonstrates how to use RealtimeSTT with auto-typing functionality
to automatically type dictated speech into any active text input field.

The transcribed speech will be automatically typed as if the user were
physically typing on the keyboard, allowing the text to appear in whatever
application currently has focus and an active text cursor.

Usage:
1. Run this script
2. Wait for the "Speak now" message
3. Open any text editor, word processor, or text input field
4. Click in the text field to give it focus
5. Speak into your microphone
6. Watch as your speech is automatically typed into the active text field

Press Ctrl+C to exit.
"""

import os
import sys
import time

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


def on_typing_start():
    """Callback when auto-typing starts"""
    print("🖊️  Auto-typing started...")


def on_typing_complete():
    """Callback when auto-typing completes"""
    print("✅ Auto-typing completed")


def on_typing_error(error):
    """Callback when auto-typing encounters an error"""
    print(f"❌ Auto-typing error: {error}")


def on_recording_start():
    """Callback when recording starts"""
    print("🎤 Recording started - speak now!")


def on_recording_stop():
    """Callback when recording stops"""
    print("⏹️  Recording stopped - processing...")


def main():
    print("🎯 RealtimeSTT Auto-Typing Example")
    print("=" * 50)
    print()
    print("This example will automatically type your speech into any active text field.")
    print("1. Open a text editor, word processor, or any text input field")
    print("2. Click in the text field to give it focus")
    print("3. Speak into your microphone")
    print("4. Watch as your speech is automatically typed!")
    print()
    print("Press Ctrl+C to exit.")
    print()
    
    try:
        # Initialize the recorder with auto-typing enabled
        recorder = AudioToTextRecorder(
            # Model configuration
            model="base",  # Use base model for good accuracy/speed balance
            language="en",  # Set to your preferred language
            
            # Auto-typing configuration
            enable_auto_typing=True,
            auto_typing_delay=0.01,  # Small delay between keystrokes
            auto_typing_fail_safe=True,  # Enable fail-safe (move mouse to corner to stop)
            auto_typing_add_space=True,  # Add space after each transcription
            
            # Auto-typing callbacks
            on_auto_typing_start=on_typing_start,
            on_auto_typing_complete=on_typing_complete,
            on_auto_typing_error=on_typing_error,
            
            # Recording callbacks
            on_recording_start=on_recording_start,
            on_recording_stop=on_recording_stop,
            
            # Voice activity detection settings
            silero_sensitivity=0.1,  # Lower = more sensitive
            webrtc_sensitivity=3,
            post_speech_silence_duration=0.5,  # Wait 0.5s after speech ends
            min_length_of_recording=0.3,  # Minimum recording length
            
            # UI settings
            spinner=True,  # Show spinner during processing
        )
        
        print("🚀 Recorder initialized successfully!")
        print("💡 Tip: Move your mouse to the top-left corner to emergency stop auto-typing")
        print()
        print("👄 Speak now...")
        
        # Main loop - continuously listen and auto-type
        while True:
            try:
                # Get transcribed text (this will automatically type it)
                text = recorder.text()
                
                if text.strip():
                    print(f"📝 Transcribed: '{text}'")
                    print("👄 Speak again...")
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                print("👄 Continuing... speak again...")
                
    except ImportError as e:
        if "pyautogui" in str(e):
            print("❌ Error: pyautogui is required for auto-typing functionality.")
            print("📦 Install it with: pip install pyautogui")
        else:
            print(f"❌ Import error: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Failed to initialize recorder: {e}")
        sys.exit(1)
        
    finally:
        try:
            recorder.shutdown()
            print("✅ Recorder shut down successfully")
        except:
            pass


if __name__ == "__main__":
    main()
