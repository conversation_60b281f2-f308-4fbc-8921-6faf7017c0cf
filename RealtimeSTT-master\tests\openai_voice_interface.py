"""
pip install realtimestt realtimetts[edge]
"""

# Set this to <PERSON>alse to start by waiting for a wake word first
# Set this to True to start directly in voice activity mode
START_IN_VOICE_ACTIVITY_MODE = False

if __name__ == '__main__':
    import os
    import sys
    from RealtimeTTS import TextToAudioStream, SystemEngine
    from RealtimeSTT import AudioToTextRecorder, GrokClient

    # Add parent directory to path for load_env import
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from load_env import get_api_key

    # Load API key from .env file
    api_key = get_api_key()
    if not api_key:
        print("Please set your XAI_API_KEY in the .env file")
        sys.exit(1)

    # Text-to-Speech Stream Setup (SystemEngine)
    engine = SystemEngine()
    stream = TextToAudioStream(
        engine,
        log_characters=True
    )

    # Speech-to-Text Recorder Setup
    recorder = AudioToTextRecorder(
        model="medium",
        language="en",
        wake_words="Jarvis",
        spinner=True,
        wake_word_activation_delay=5 if START_IN_VOICE_ACTIVITY_MODE else 0,
    )

    # Initialize Grok client
    grok_client = GrokClient(api_key=api_key)

    system_prompt_message = {
        'role': 'system',
        'content': 'Answer precise and short with the polite sarcasm of a butler.'
    }

    def generate_response(messages):
        """Generate assistant's response using Grok."""
        for chunk in grok_client.chat_completion(
            model="grok-4",
            messages=messages,
            stream=True
        ):
            if "choices" in chunk and len(chunk["choices"]) > 0:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta and delta["content"]:
                    yield delta["content"]

    history = []

    try:
        # Main loop for interaction
        while True:
            if START_IN_VOICE_ACTIVITY_MODE:
                print("Please speak...")
            else:
                print('Say "Jarvis" then speak...')

            user_text = recorder.text().strip()

            # If not starting in voice activity mode, set the delay after the first interaction
            if not START_IN_VOICE_ACTIVITY_MODE:
                recorder.wake_word_activation_delay = 5

            print(f"Transcribed: {user_text}")

            if not user_text:
                continue

            print(f'>>> {user_text}\n<<< ', end="", flush=True)
            history.append({'role': 'user', 'content': user_text})

            # Get assistant response and play it
            assistant_response = generate_response([system_prompt_message] + history[-10:])
            stream.feed(assistant_response).play()

            history.append({'role': 'assistant', 'content': stream.text()})
    except KeyboardInterrupt:
        print("\nKeyboard interrupt detected. Shutting down...")
        recorder.shutdown()
