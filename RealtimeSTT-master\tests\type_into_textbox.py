"""
Updated type_into_textbox.py using built-in auto-typing functionality

This example demonstrates the new built-in auto-typing feature of RealtimeSTT.
Instead of manually handling pyautogui, we now use the enable_auto_typing parameter.

The transcribed speech will be automatically typed into any active text input field.
"""

from RealtimeSTT import AudioToTextRecorder

if __name__ == '__main__':
    print("🎯 Auto-typing example using built-in functionality")
    print("Open a text field and start speaking!")
    print("Press Ctrl+C to exit.")

    try:
        # Use the new built-in auto-typing functionality
        recorder = AudioToTextRecorder(
            enable_auto_typing=True,  # Enable built-in auto-typing
            auto_typing_delay=0.01,   # Typing speed
            auto_typing_add_space=True,  # Add space after each transcription
            spinner=True
        )

        print("✅ Ready! Speak now...")

        while True:
            # The text will be automatically typed - no manual callback needed
            text = recorder.text()
            if text.strip():
                print(f"Auto-typed: '{text}'")

    except KeyboardInterrupt:
        print("\nExiting...")
    except ImportError as e:
        if "pyautogui" in str(e):
            print("❌ Error: pyautogui is required for auto-typing.")
            print("Install it with: pip install pyautogui")
        else:
            print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        try:
            recorder.shutdown()
        except:
            pass