import RealtimeSTT, RealtimeTTS
import os
import sys

# Add parent directory to path for load_env import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from load_env import get_api_key

if __name__ == '__main__':
    # Load API key from .env file
    api_key = get_api_key()
    if not api_key:
        print("Please set your XAI_API_KEY in the .env file")
        sys.exit(1)

    grok_client = RealtimeSTT.GrokClient(api_key=api_key)
    character_prompt = 'Answer precise and short with the polite sarcasm of a butler.'
    stream = RealtimeTTS.TextToAudioStream(RealtimeTTS.SystemEngine(), log_characters=True)
    recorder = RealtimeSTT.AudioToTextRecorder(model="medium")

    def generate(messages):
        for chunk in grok_client.chat_completion(model="grok-4", messages=messages, stream=True):
            if "choices" in chunk and len(chunk["choices"]) > 0:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta and delta["content"]:
                    yield delta["content"]

    history = []
    while True:
        print("\n\nSpeak when ready")
        print(f'>>> {(user_text := recorder.text())}\n<<< ', end="", flush=True)
        history.append({'role': 'user', 'content': user_text})
        assistant_response = generate([{ 'role': 'system',  'content': character_prompt}] + history[-10:])
        stream.feed(assistant_response).play()
        history.append({'role': 'assistant', 'content': stream.text()})