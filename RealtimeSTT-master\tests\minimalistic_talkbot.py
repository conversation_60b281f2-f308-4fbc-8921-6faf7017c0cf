import RealtimeSTT, RealtimeTTS
import os

if __name__ == '__main__':
    grok_client = RealtimeSTT.GrokClient(api_key=os.environ.get("XAI_API_KEY"))
    character_prompt = 'Answer precise and short with the polite sarcasm of a butler.'
    stream = RealtimeTTS.TextToAudioStream(RealtimeTTS.SystemEngine(), log_characters=True)
    recorder = RealtimeSTT.AudioToTextRecorder(model="medium")

    def generate(messages):
        for chunk in grok_client.chat_completion(model="grok-4", messages=messages, stream=True):
            if "choices" in chunk and len(chunk["choices"]) > 0:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta and delta["content"]:
                    yield delta["content"]

    history = []
    while True:
        print("\n\nSpeak when ready")
        print(f'>>> {(user_text := recorder.text())}\n<<< ', end="", flush=True)
        history.append({'role': 'user', 'content': user_text})
        assistant_response = generate([{ 'role': 'system',  'content': character_prompt}] + history[-10:])
        stream.feed(assistant_response).play()
        history.append({'role': 'assistant', 'content': stream.text()})